<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $__env->yieldContent('title', $settings['site_name'] ?? 'บริการจัดงานศพ'); ?></title>
    <meta name="description" content="<?php echo $__env->yieldContent('description', $settings['site_description'] ?? 'ให้เราดูแลในช่วงเวลาที่ยากลำบาก ด้วยความเคารพและเอาใจใส่'); ?>")
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo e(asset('css/funeral-style.css')); ?>" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', 'Kanit', sans-serif;
            background-color: #fafafa;
            margin: 0;
            padding: 0;
        }

        /* แก้ไขปัญหาแถบด้านบนบังเนื้อหา */
        .navbar.sticky-top {
            top: 0 !important;
            z-index: 1030;
            margin-bottom: 0 !important;
        }

        /* เพิ่ม padding-top ให้กับเนื้อหาหลักเพื่อชดเชยความสูงของ navbar */
        main {
            padding-top: 80px !important; /* ปรับตามความสูงของ navbar */
            margin-top: 0 !important;
        }

        /* สำหรับ hero section ที่เป็น section แรก */
        main > section:first-child.hero-section {
            margin-top: -80px !important; /* ดึงขึ้นมาให้ติดกับ navbar */
            padding-top: 80px !important; /* แต่ยังคงมี padding ด้านบน */
        }

        /* สำหรับ carousel ใน hero section */
        .hero-section .carousel,
        .hero-section .carousel-inner,
        .hero-section .carousel-item,
        .hero-section .banner-slide {
            margin-top: 0 !important;
            border-top: none !important;
        }

        /* ปรับ hero content overlay ให้ไม่ถูกบัง */
        .hero-content-overlay {
            padding-top: 100px !important; /* เพิ่ม padding เพื่อไม่ให้ข้อความถูกบัง */
        }

        /* สำหรับหน้าที่ไม่มี hero section */
        main > section:first-child:not(.hero-section) {
            padding-top: 20px !important;
        }

        /* Package price styling */
        .price-display {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
            white-space: nowrap;
        }

        .card-title {
            color: #2c3e50;
            font-weight: 600;
        }

        /* Package card hover effects */
        .package-card {
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .package-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .package-card .card-hover-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 0.375rem 0.375rem 0 0;
        }

        .package-card:hover .card-hover-overlay {
            opacity: 1;
        }

        /* Responsive price display */
        @media (max-width: 768px) {
            .price-display {
                font-size: 0.9rem;
                padding: 6px 12px;
            }
        }
    </style>
    
    <?php echo $__env->yieldContent('styles'); ?>
</head>
<body>
    <!-- Navigation - สวยงามเรียบง่าย -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <!-- โลโก้หน้าบ้าน - ใช้ไฟล์ image (2).png -->
            <!-- สำหรับเปลี่ยนโลโก้: ใส่รูปใหม่ในโฟลเดอร์ public/images/ และตั้งชื่อเป็น image (2).png -->
            <a class="navbar-brand fw-bold text-primary d-flex align-items-center" href="<?php echo e(route('home')); ?>">
                <img src="<?php echo e(asset('images/bridxx (1).png')); ?>" alt="โลโก้หลัก" class="me-2" style="height: 60px; width: auto;">
                <span><?php echo e($settings['site_name'] ?? 'ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป'); ?></span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>" href="<?php echo e(route('home')); ?>">หน้าหลัก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('services') ? 'active' : ''); ?>" href="<?php echo e(route('services')); ?>">บริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('packages') ? 'active' : ''); ?>" href="<?php echo e(route('packages')); ?>">แพคเกจ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('activities') ? 'active' : ''); ?>" href="<?php echo e(route('activities')); ?>">ผลงาน</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('contact') ? 'active' : ''); ?>" href="<?php echo e(route('contact')); ?>">ติดต่อเรา</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('admin.login')); ?>">
                            <i class="fas fa-user-shield me-1"></i>เข้าสู่ระบบ
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Footer -->
    <footer class="footer mt-5 py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo e($settings['site_name'] ?? 'บริการจัดงานศพ'); ?></h5>
                    <p><?php echo e($settings['site_description'] ?? 'ให้เราดูแลในช่วงเวลาที่ยากลำบาก ด้วยความเคารพและเอาใจใส่'); ?></p>
                </div>
                <div class="col-md-4">
                    <h5>ติดต่อเรา</h5>
                    <p><i class="fas fa-phone"></i> <?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?></p>
                    <p><i class="fas fa-envelope"></i> <?php echo e($settings['contact_email'] ?? '<EMAIL>'); ?></p>
                    <p><i class="fas fa-map-marker-alt"></i> <?php echo e($settings['contact_address'] ?? 'ที่อยู่บริษัท'); ?></p>
                </div>
                <div class="col-md-4">
                    <h5>ติดตามเรา</h5>
                    <div class="d-flex gap-3">
                        <?php if(!empty($settings['facebook_url'])): ?>
                        <a href="<?php echo e($settings['facebook_url']); ?>" class="text-white" target="_blank">
                            <i class="fab fa-facebook fa-2x"></i>
                        </a>
                        <?php endif; ?>
                        <?php if(!empty($settings['line_id'])): ?>
                        <a href="https://line.me/ti/p/<?php echo e($settings['line_id']); ?>" class="text-white" target="_blank">
                            <i class="fab fa-line fa-2x"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; <?php echo e(date('Y')); ?> <?php echo e($settings['site_name'] ?? 'บริการจัดงานศพ'); ?>. สงวนลิขสิทธิ์.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Global Functions -->
    <script>
        // Global Back Button Function - ฟังก์ชันย้อนกลับสำหรับทุกหน้า
        function goBack() {
            // ตรวจสอบว่ามี history หรือไม่
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // ถ้าไม่มี history ให้กลับไปหน้าหลัก
                window.location.href = '<?php echo e(route("home")); ?>';
            }
        }

        // Enhanced Back Function with specific fallback routes
        function goBackTo(fallbackRoute) {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = fallbackRoute;
            }
        }
    </script>

    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/layouts/app.blade.php ENDPATH**/ ?>