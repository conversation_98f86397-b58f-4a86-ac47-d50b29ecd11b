// Phuyai Prajak Service Shop Admin - Navbar Overlap Fix
// ป้องกันการบังของ navbar กับเนื้อหา

document.addEventListener('DOMContentLoaded', function() {
    
    // ฟังก์ชันตรวจสอบและปรับปรุงตำแหน่งเนื้อหา
    function adjustContentPosition() {
        const navbar = document.querySelector('.top-navbar');
        const mainWrapper = document.querySelector('.main-wrapper');
        const contentSafeArea = document.querySelector('.content-safe-area');
        
        if (!navbar || !mainWrapper) return;
        
        // คำนวณความสูงของ navbar
        const navbarHeight = navbar.offsetHeight;
        const currentMarginTop = parseInt(window.getComputedStyle(mainWrapper).marginTop);
        
        // ตรวจสอบว่า margin-top เพียงพอหรือไม่
        if (currentMarginTop < navbarHeight + 15) {
            mainWrapper.style.marginTop = (navbarHeight + 15) + 'px';
            console.log('Adjusted main-wrapper margin-top to:', navbarHeight + 15 + 'px');
        }
        
        // เพิ่ม padding-top สำหรับ content-safe-area หากจำเป็น
        if (contentSafeArea) {
            const rect = contentSafeArea.getBoundingClientRect();
            if (rect.top < navbarHeight) {
                contentSafeArea.style.paddingTop = '2rem';
                contentSafeArea.style.marginTop = '1rem';
                console.log('Adjusted content-safe-area padding');
            }
        }
    }
    
    // ฟังก์ชันตรวจสอบการทับซ้อน
    function checkOverlap() {
        const navbar = document.querySelector('.top-navbar');
        const firstElement = document.querySelector('.content-safe-area h1, .content-safe-area .h2, .content-safe-area .row:first-child');
        
        if (!navbar || !firstElement) return;
        
        const navbarRect = navbar.getBoundingClientRect();
        const elementRect = firstElement.getBoundingClientRect();
        
        // ตรวจสอบว่ามีการทับซ้อนหรือไม่
        if (elementRect.top < navbarRect.bottom + 10) {
            console.warn('Content overlap detected! Adjusting...');
            
            // เพิ่ม margin-top ให้กับ element แรก
            firstElement.style.marginTop = '2rem';
            
            // หรือเพิ่ม padding-top ให้กับ parent container
            const parent = firstElement.closest('.content-safe-area') || firstElement.closest('.main-wrapper');
            if (parent) {
                parent.style.paddingTop = '2rem';
            }
        }
    }
    
    // ฟังก์ชันสำหรับ responsive adjustment
    function handleResponsiveAdjustment() {
        const screenWidth = window.innerWidth;
        const navbar = document.querySelector('.top-navbar');
        const mainWrapper = document.querySelector('.main-wrapper');
        
        if (!navbar || !mainWrapper) return;
        
        if (screenWidth <= 768) {
            // Mobile adjustments
            navbar.style.height = '75px';
            mainWrapper.style.marginTop = '85px';
            mainWrapper.style.paddingTop = '0.5rem';
        } else {
            // Desktop adjustments
            navbar.style.height = '70px';
            mainWrapper.style.marginTop = '85px';
            mainWrapper.style.paddingTop = '1rem';
        }
    }
    
    // เรียกใช้ฟังก์ชันเมื่อโหลดหน้า
    adjustContentPosition();
    checkOverlap();
    handleResponsiveAdjustment();
    
    // เรียกใช้ฟังก์ชันเมื่อ resize หน้าจอ
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            adjustContentPosition();
            checkOverlap();
            handleResponsiveAdjustment();
        }, 250);
    });
    
    // เรียกใช้ฟังก์ชันเมื่อ scroll (สำหรับ sticky navbar)
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(function() {
            checkOverlap();
        }, 100);
    });
    
    // ฟังก์ชันสำหรับ smooth scroll ที่คำนึงถึง navbar
    function smoothScrollToElement(element, offset = 85) {
        if (!element) return;
        
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - offset;
        
        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }
    
    // เพิ่มฟังก์ชัน smooth scroll ให้กับ anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                smoothScrollToElement(target);
            }
        });
    });
    
    // ฟังก์ชันสำหรับ debug (เฉพาะ development)
    function debugNavbarPosition() {
        const navbar = document.querySelector('.top-navbar');
        const mainWrapper = document.querySelector('.main-wrapper');
        
        if (navbar && mainWrapper) {
            console.log('Navbar height:', navbar.offsetHeight);
            console.log('Main wrapper margin-top:', window.getComputedStyle(mainWrapper).marginTop);
            console.log('Main wrapper padding-top:', window.getComputedStyle(mainWrapper).paddingTop);
        }
    }
    
    // เรียกใช้ debug function (สามารถลบออกได้ใน production)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        debugNavbarPosition();
    }
    
    console.log('Admin navbar overlap fix initialized successfully! 🚀');
});

// Export functions สำหรับใช้งานภายนอก
window.AdminNavbarFix = {
    adjustContentPosition: function() {
        // สามารถเรียกใช้จากภายนอกได้
        const event = new Event('DOMContentLoaded');
        document.dispatchEvent(event);
    },
    
    checkOverlap: function() {
        // ตรวจสอบการทับซ้อนแบบ manual
        const navbar = document.querySelector('.top-navbar');
        const firstElement = document.querySelector('.content-safe-area h1, .content-safe-area .h2');
        
        if (navbar && firstElement) {
            const navbarRect = navbar.getBoundingClientRect();
            const elementRect = firstElement.getBoundingClientRect();
            
            return elementRect.top < navbarRect.bottom + 10;
        }
        
        return false;
    }
};
